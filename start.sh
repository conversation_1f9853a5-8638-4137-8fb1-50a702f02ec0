#!/bin/bash

echo ""
echo "========================================"
echo "    Prompt Tools 服务器启动脚本"
echo "========================================"
echo ""

echo "🔄 正在检查 Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js"
    echo "请先安装 Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js 已安装"
echo ""

echo "🔄 正在检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "✅ 依赖检查完成"
echo ""

echo "🔄 正在构建前端..."
npm run client:build
if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建完成"
echo ""

echo "🚀 正在启动 Prompt Tools 服务器..."
echo ""
echo "服务器将在以下地址启动:"
echo "📍 http://localhost:18080"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

node server/src/app.js
