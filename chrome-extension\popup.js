// Prompt Tools Chrome Extension - Popup Script
class PromptToolsPopup {
  constructor() {
    this.apiBase = 'http://localhost:18080/api';
    this.prompts = [];
    this.filteredPrompts = [];
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadPrompts();
  }

  bindEvents() {
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadPrompts();
    });

    // 打开网页版按钮
    document.getElementById('openWebBtn').addEventListener('click', () => {
      chrome.tabs.create({ url: 'http://localhost:18080' });
    });

    // 重试按钮
    document.getElementById('retryBtn').addEventListener('click', () => {
      this.loadPrompts();
    });

    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    
    searchInput.addEventListener('input', (e) => {
      this.filterPrompts(e.target.value);
    });
    
    searchBtn.addEventListener('click', () => {
      this.filterPrompts(searchInput.value);
    });

    // 回车搜索
    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.filterPrompts(e.target.value);
      }
    });
  }

  async loadPrompts() {
    this.showLoading();
    
    try {
      const response = await fetch(`${this.apiBase}/prompts`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.prompts = await response.json();
      this.filteredPrompts = [...this.prompts];
      this.renderPrompts();
      this.hideLoading();
      
    } catch (error) {
      console.error('加载提示词失败:', error);
      this.showError();
    }
  }

  filterPrompts(query) {
    if (!query.trim()) {
      this.filteredPrompts = [...this.prompts];
    } else {
      const searchTerm = query.toLowerCase();
      this.filteredPrompts = this.prompts.filter(prompt => 
        prompt.name.toLowerCase().includes(searchTerm) ||
        prompt.content.toLowerCase().includes(searchTerm) ||
        (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm))) ||
        (prompt.source && prompt.source.toLowerCase().includes(searchTerm))
      );
    }
    this.renderPrompts();
  }

  renderPrompts() {
    const container = document.getElementById('promptsList');
    const emptyState = document.getElementById('emptyState');
    
    if (this.filteredPrompts.length === 0) {
      container.classList.add('hidden');
      emptyState.classList.remove('hidden');
      return;
    }

    emptyState.classList.add('hidden');
    container.classList.remove('hidden');
    
    container.innerHTML = this.filteredPrompts.map(prompt => this.createPromptHTML(prompt)).join('');
    
    // 绑定点击事件
    container.querySelectorAll('.prompt-item').forEach((item, index) => {
      item.addEventListener('click', () => {
        this.copyPrompt(this.filteredPrompts[index], item);
      });
    });
  }

  createPromptHTML(prompt) {
    const tags = prompt.tags || [];
    const tagsHTML = tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('');
    
    return `
      <div class="prompt-item" data-id="${prompt.id}">
        <div class="prompt-header">
          <div class="prompt-name">${this.escapeHtml(prompt.name)}</div>
          ${prompt.source ? `<div class="prompt-source">${this.escapeHtml(prompt.source)}</div>` : ''}
        </div>
        <div class="prompt-content">${this.escapeHtml(prompt.content)}</div>
        ${tags.length > 0 ? `<div class="prompt-tags">${tagsHTML}</div>` : ''}
        <div class="copy-indicator">已复制!</div>
      </div>
    `;
  }

  async copyPrompt(prompt, element) {
    try {
      await navigator.clipboard.writeText(prompt.content);
      
      // 显示复制成功效果
      element.classList.add('copied');
      const indicator = element.querySelector('.copy-indicator');
      indicator.classList.add('show');
      
      // 显示状态消息
      this.showStatus('✅ 已复制到剪贴板', 'success');
      
      // 重置样式
      setTimeout(() => {
        element.classList.remove('copied');
        indicator.classList.remove('show');
      }, 1500);
      
    } catch (error) {
      console.error('复制失败:', error);
      this.showStatus('❌ 复制失败', 'error');
    }
  }

  showLoading() {
    document.getElementById('loadingSpinner').classList.remove('hidden');
    document.getElementById('errorMessage').classList.add('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
  }

  hideLoading() {
    document.getElementById('loadingSpinner').classList.add('hidden');
  }

  showError() {
    document.getElementById('loadingSpinner').classList.add('hidden');
    document.getElementById('errorMessage').classList.remove('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
  }

  showStatus(message, type = 'success') {
    const statusEl = document.getElementById('statusMessage');
    statusEl.textContent = message;
    statusEl.className = `status-message ${type}`;
    statusEl.classList.remove('hidden');
    
    setTimeout(() => {
      statusEl.classList.add('hidden');
    }, 3000);
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
  new PromptToolsPopup();
});
