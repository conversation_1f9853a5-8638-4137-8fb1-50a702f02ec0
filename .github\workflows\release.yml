name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  prepare:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Type check
        run: pnpm typecheck
      - name: Build frontend
        run: pnpm build

  build:
    needs: prepare
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-22.04
            platform: linux
            arch: x64
          - os: windows-2022
            platform: windows
            arch: x64
          - os: macos-13
            platform: macos
            arch: x64
            targets: x86_64-apple-darwin
          - os: macos-14
            platform: macos
            arch: arm64
            targets: aarch64-apple-darwin
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install system deps for Linux (Ubuntu 22.04)
        if: ${{ matrix.os == 'ubuntu-22.04' }}
        run: |
          sudo apt-get update && sudo apt-get install -y \
            libwebkit2gtk-4.1-dev libgtk-3-dev libayatana-appindicator3-dev \
            librsvg2-dev build-essential curl wget file patchelf libssl-dev \
            xdg-utils desktop-file-utils

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.targets || '' }}

      - name: Cache cargo registry
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            src-tauri/target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-

      - name: Extract version from tag
        shell: bash
        run: |
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "Extracted version: $VERSION"

      - name: Update version in tauri.conf.json
        shell: bash
        run: |
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            sed -i '' "s/\"version\": \".*\"/\"version\": \"$VERSION\"/" src-tauri/tauri.conf.json
          else
            sed -i "s/\"version\": \".*\"/\"version\": \"$VERSION\"/" src-tauri/tauri.conf.json
          fi
          echo "Updated tauri.conf.json version to: $VERSION"

      - name: Update version in Cargo.toml
        shell: bash
        run: |
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            sed -i '' "s/^version = \".*\"/version = \"$VERSION\"/" src-tauri/Cargo.toml
          else
            sed -i "s/^version = \".*\"/version = \"$VERSION\"/" src-tauri/Cargo.toml
          fi
          echo "Updated Cargo.toml version to: $VERSION"

      - name: Build Tauri app
        run: pnpm tauri build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_PRIVATE_KEY }}
          TAURI_PRIVATE_KEY: ${{ secrets.TAURI_PRIVATE_KEY }}
          TAURI_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}
          TAURI_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}

      - name: List generated bundles (Debug)
        run: |
          echo "=== Generated bundles for ${{ matrix.platform }}-${{ matrix.arch }} ==="
          if ($env:OS -eq "Windows_NT") {
            Get-ChildItem -Recurse src-tauri/target/release/bundle | Sort-Object FullName | ForEach-Object { $_.FullName }
          } else {
            find src-tauri/target/release/bundle -type f | sort || echo "No bundles found"
          }
        shell: pwsh

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-${{ matrix.platform }}-${{ matrix.arch }}
          path: |
            src-tauri/target/release/bundle/dmg/*.dmg
            src-tauri/target/release/bundle/macos/*.app
            src-tauri/target/release/bundle/appimage/*.AppImage
            src-tauri/target/release/bundle/deb/*.deb
            src-tauri/target/release/bundle/rpm/*.rpm
            src-tauri/target/release/bundle/nsis/*.exe
            src-tauri/target/release/bundle/msi/*.msi
          retention-days: 7

  release:
    needs: build
    runs-on: ubuntu-22.04
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
      
      - name: Display structure of downloaded files
        run: ls -R artifacts
      
      - name: Rename files to avoid conflicts
        run: |
          echo "Processing macOS x64 files..."
          find artifacts/prompt-tools-macos-x64 -name "*.app" -type d | while read file; do
            if [ -d "$file" ]; then
              dir=$(dirname "$file")
              filename=$(basename "$file")
              newname=$(echo "$filename" | sed 's/\.app/\.x64\.app/')
              mv "$file" "$dir/$newname"
              echo "Renamed $filename to $newname"
            fi
          done

          echo "Processing macOS ARM64 files..."
          find artifacts/prompt-tools-macos-arm64 -name "*.app" -type d | while read file; do
            if [ -d "$file" ]; then
              dir=$(dirname "$file")
              filename=$(basename "$file")
              newname=$(echo "$filename" | sed 's/\.app/\.arm64\.app/')
              mv "$file" "$dir/$newname"
              echo "Renamed $filename to $newname"
            fi
          done

          echo "=== File renaming completed ==="
          ls -R artifacts
      
      - name: Create GitHub Release and upload assets
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          name: Release ${{ github.ref_name }}
          files: |
            artifacts/**/*.dmg
            artifacts/**/*.app
            artifacts/**/*.AppImage
            artifacts/**/*.deb
            artifacts/**/*.rpm
            artifacts/**/*setup*.exe
            artifacts/**/*.msi
          generate_release_notes: true
          draft: false
          prerelease: ${{ contains(github.ref_name, '-') }}