import database from '../models/database.js';

class PromptController {
  // 获取所有提示词
  static getAllPrompts(req, res) {
    try {
      const query = `
        SELECT p.id, p.name, p.source, p.notes, p.tags, p.pinned,
               p.created_at, p.updated_at, p.current_version_id,
               v.content, v.version
        FROM prompts p
        LEFT JOIN versions v ON v.id = p.current_version_id
        ORDER BY p.pinned DESC, p.updated_at DESC
      `;

      const result = database.getDB().exec(query);
      const rows = result.length > 0 ? result[0].values : [];

      const prompts = rows.map(row => ({
        id: row[0],
        name: row[1],
        source: row[2],
        notes: row[3],
        tags: row[4] ? JSON.parse(row[4]) : [],
        pinned: row[5] === 1,
        created_at: row[6],
        updated_at: row[7],
        current_version_id: row[8],
        content: row[9] || '',
        version: row[10] || '1.0.0'
      }));

      res.json(prompts);
    } catch (err) {
      console.error('获取提示词失败:', err);
      res.status(500).json({ error: '获取提示词失败', message: err.message });
    }
  }

  // 搜索提示词
  static searchPrompts(req, res) {
    try {
      const { query = '', tags = [], sources = [] } = req.body;

      let sql = `
        SELECT p.id, p.name, p.source, p.notes, p.tags, p.pinned,
               p.created_at, p.updated_at, p.current_version_id,
               v.content, v.version
        FROM prompts p
        LEFT JOIN versions v ON v.id = p.current_version_id
      `;

      const conditions = [];
      const params = [];

      if (query) {
        const queryPattern = `%${query}%`;
        conditions.push('(p.name LIKE ? OR p.source LIKE ? OR p.notes LIKE ? OR p.tags LIKE ? OR v.content LIKE ?)');
        params.push(queryPattern, queryPattern, queryPattern, queryPattern, queryPattern);
      }

      if (tags.length > 0) {
        const tagConditions = tags.map(() => 'p.tags LIKE ?').join(' OR ');
        conditions.push(`(${tagConditions})`);
        tags.forEach(tag => params.push(`%"${tag}":%`));
      }

      if (sources.length > 0) {
        const sourceConditions = sources.map(() => 'p.source = ?').join(' OR ');
        conditions.push(`(${sourceConditions})`);
        sources.forEach(source => params.push(source));
      }

      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      sql += ' ORDER BY p.pinned DESC, p.updated_at DESC';

      const result = database.getDB().exec(sql, params);
      const rows = result.length > 0 ? result[0].values : [];

      const prompts = rows.map(row => ({
        id: row[0],
        name: row[1],
        source: row[2],
        notes: row[3],
        tags: row[4] ? JSON.parse(row[4]) : [],
        pinned: row[5] === 1,
        created_at: row[6],
        updated_at: row[7],
        current_version_id: row[8],
        content: row[9] || '',
        version: row[10] || '1.0.0'
      }));

      // 获取所有标签和来源
      const allTags = PromptController.getAllTagsSync();
      const allSources = PromptController.getAllSourcesSync();

      res.json({
        prompts,
        total: prompts.length,
        tags: allTags,
        sources: allSources
      });
    } catch (err) {
      console.error('搜索提示词失败:', err);
      res.status(500).json({ error: '搜索失败', message: err.message });
    }
  }

  // 创建提示词
  static createPrompt(req, res) {
    try {
      const { name, source, notes, tags, content } = req.body;

      if (!name || !content) {
        return res.status(400).json({ error: '名称和内容不能为空' });
      }

      const now = new Date().toISOString();
      const tagsJson = JSON.stringify(tags || []);

      // 创建提示词
      database.getDB().run(`
        INSERT INTO prompts (name, source, notes, tags, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [name, source || null, notes || null, tagsJson, now, now]);

      // 获取插入的提示词ID
      const promptResult = database.getDB().exec('SELECT last_insert_rowid() as id');
      const promptId = promptResult[0].values[0][0];

      // 创建初始版本
      database.getDB().run(`
        INSERT INTO versions (prompt_id, version, content, created_at, parent_version_id)
        VALUES (?, '1.0.0', ?, ?, NULL)
      `, [promptId, content, now]);

      // 获取插入的版本ID
      const versionResult = database.getDB().exec('SELECT last_insert_rowid() as id');
      const versionId = versionResult[0].values[0][0];

      // 更新当前版本ID
      database.getDB().run('UPDATE prompts SET current_version_id = ? WHERE id = ?', [versionId, promptId]);

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 创建提示词成功: ${name} (ID: ${promptId})`);
      res.json({ id: promptId, message: '创建成功' });
    } catch (err) {
      console.error('创建提示词失败:', err);
      res.status(500).json({ error: '创建失败', message: err.message });
    }
  }

  // 更新提示词
  static updatePrompt(req, res) {
    try {
      const { id } = req.params;
      const { name, source, notes, tags, content, save_as_version = false, version_type = 'patch' } = req.body;

      if (!name || !content) {
        return res.status(400).json({ error: '名称和内容不能为空' });
      }

      const now = new Date().toISOString();
      const tagsJson = JSON.stringify(tags || []);

      // 更新基本信息
      database.getDB().run(`
        UPDATE prompts SET name = ?, source = ?, notes = ?, tags = ?, updated_at = ?
        WHERE id = ?
      `, [name, source || null, notes || null, tagsJson, now, id]);

      let newVersion = null;

      if (save_as_version) {
        // 获取当前版本号
        const currentVersionResult = database.getDB().exec(`
          SELECT v.version FROM versions v
          JOIN prompts p ON p.current_version_id = v.id
          WHERE p.id = ?
        `, [id]);

        const currentVersion = currentVersionResult.length > 0 && currentVersionResult[0].values.length > 0
          ? currentVersionResult[0].values[0][0] : '1.0.0';
        newVersion = PromptController.bumpVersion(currentVersion, version_type);

        // 获取当前版本ID作为父版本
        const parentVersionResult = database.getDB().exec(`
          SELECT current_version_id FROM prompts WHERE id = ?
        `, [id]);
        const parentVersionId = parentVersionResult.length > 0 && parentVersionResult[0].values.length > 0
          ? parentVersionResult[0].values[0][0] : null;

        // 创建新版本
        database.getDB().run(`
          INSERT INTO versions (prompt_id, version, content, created_at, parent_version_id)
          VALUES (?, ?, ?, ?, ?)
        `, [id, newVersion, content, now, parentVersionId]);

        // 获取新版本ID
        const newVersionResult = database.getDB().exec('SELECT last_insert_rowid() as id');
        const newVersionId = newVersionResult[0].values[0][0];

        // 更新当前版本ID
        database.getDB().run('UPDATE prompts SET current_version_id = ? WHERE id = ?', [newVersionId, id]);
      } else {
        // 仅更新当前版本内容
        database.getDB().run(`
          UPDATE versions SET content = ?
          WHERE id = (SELECT current_version_id FROM prompts WHERE id = ?)
        `, [content, id]);
      }

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 更新提示词成功: ${name} (ID: ${id})`);

      if (newVersion) {
        res.json({ message: '更新成功', version: newVersion });
      } else {
        res.json({ message: '更新成功' });
      }
    } catch (err) {
      console.error('更新提示词失败:', err);
      res.status(500).json({ error: '更新失败', message: err.message });
    }
  }

  // 版本号递增
  static bumpVersion(current, versionType) {
    const parts = current.split('.');
    if (parts.length !== 3) {
      return '1.0.0';
    }

    let major = parseInt(parts[0]) || 1;
    let minor = parseInt(parts[1]) || 0;
    let patch = parseInt(parts[2]) || 0;

    switch (versionType) {
      case 'major':
        return `${major + 1}.0.0`;
      case 'minor':
        return `${major}.${minor + 1}.0`;
      default: // patch
        return `${major}.${minor}.${patch + 1}`;
    }
  }

  // 删除提示词
  static deletePrompt(req, res) {
    try {
      const { id } = req.params;

      // 先删除所有相关版本
      database.getDB().run('DELETE FROM versions WHERE prompt_id = ?', [id]);

      // 再删除提示词
      database.getDB().run('DELETE FROM prompts WHERE id = ?', [id]);

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 删除提示词成功 (ID: ${id})`);
      res.json({ message: '删除成功' });
    } catch (err) {
      console.error('删除提示词失败:', err);
      res.status(500).json({ error: '删除失败', message: err.message });
    }
  }

  // 切换置顶状态
  static togglePin(req, res) {
    try {
      const { id } = req.params;
      const now = new Date().toISOString();

      database.getDB().run(`
        UPDATE prompts SET pinned = NOT pinned, updated_at = ? WHERE id = ?
      `, [now, id]);

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 切换置顶状态成功 (ID: ${id})`);
      res.json({ message: '操作成功' });
    } catch (err) {
      console.error('切换置顶失败:', err);
      res.status(500).json({ error: '操作失败', message: err.message });
    }
  }

  // 获取所有标签（同步版本）
  static getAllTagsSync() {
    try {
      const result = database.getDB().exec(`
        SELECT DISTINCT tags FROM prompts WHERE tags IS NOT NULL AND tags != ""
      `);

      const rows = result.length > 0 ? result[0].values : [];
      const allTags = new Set();

      rows.forEach(row => {
        try {
          const tags = JSON.parse(row[0]);
          tags.forEach(tag => allTags.add(tag));
        } catch (e) {
          // 忽略解析错误
        }
      });

      return Array.from(allTags).sort();
    } catch (err) {
      console.error('获取标签失败:', err);
      return [];
    }
  }

  // 获取所有来源（同步版本）
  static getAllSourcesSync() {
    try {
      const result = database.getDB().exec(`
        SELECT DISTINCT source FROM prompts WHERE source IS NOT NULL AND source != ""
      `);

      const rows = result.length > 0 ? result[0].values : [];
      return rows.map(row => row[0]).sort();
    } catch (err) {
      console.error('获取来源失败:', err);
      return [];
    }
  }

  // API端点：获取所有标签
  static getTagsEndpoint(req, res) {
    try {
      const tags = PromptController.getAllTagsSync();
      res.json(tags);
    } catch (err) {
      console.error('获取标签失败:', err);
      res.status(500).json({ error: '获取标签失败', message: err.message });
    }
  }

  // API端点：获取所有来源
  static getSourcesEndpoint(req, res) {
    try {
      const sources = PromptController.getAllSourcesSync();
      res.json(sources);
    } catch (err) {
      console.error('获取来源失败:', err);
      res.status(500).json({ error: '获取来源失败', message: err.message });
    }
  }

  // 获取提示词版本历史
  static getPromptVersions(req, res) {
    try {
      const { id } = req.params;

      const result = database.getDB().exec(`
        SELECT id, prompt_id, version, content, created_at, parent_version_id
        FROM versions WHERE prompt_id = ? ORDER BY created_at DESC
      `, [id]);

      const rows = result.length > 0 ? result[0].values : [];
      const versions = rows.map(row => ({
        id: row[0],
        prompt_id: row[1],
        version: row[2],
        content: row[3],
        created_at: row[4],
        parent_version_id: row[5]
      }));

      res.json(versions);
    } catch (err) {
      console.error('获取版本历史失败:', err);
      res.status(500).json({ error: '获取版本历史失败', message: err.message });
    }
  }

  // 回滚到指定版本
  static rollbackToVersion(req, res) {
    try {
      const { id } = req.params;
      const { version_id, version_type = 'patch' } = req.body;

      const now = new Date().toISOString();

      // 获取目标版本内容
      const targetResult = database.getDB().exec(`
        SELECT content FROM versions WHERE id = ? AND prompt_id = ?
      `, [version_id, id]);

      if (targetResult.length === 0 || targetResult[0].values.length === 0) {
        return res.status(404).json({ error: '目标版本不存在' });
      }

      const targetContent = targetResult[0].values[0][0];

      // 获取当前版本号
      const currentVersionResult = database.getDB().exec(`
        SELECT v.version FROM versions v
        JOIN prompts p ON p.current_version_id = v.id
        WHERE p.id = ?
      `, [id]);

      const currentVersion = currentVersionResult.length > 0 && currentVersionResult[0].values.length > 0
        ? currentVersionResult[0].values[0][0] : '1.0.0';
      const newVersion = PromptController.bumpVersion(currentVersion, version_type);

      // 创建新版本
      database.getDB().run(`
        INSERT INTO versions (prompt_id, version, content, created_at, parent_version_id)
        VALUES (?, ?, ?, ?, ?)
      `, [id, newVersion, targetContent, now, version_id]);

      // 获取新版本ID
      const newVersionResult = database.getDB().exec('SELECT last_insert_rowid() as id');
      const newVersionId = newVersionResult[0].values[0][0];

      // 更新当前版本ID和更新时间
      database.getDB().run(`
        UPDATE prompts SET current_version_id = ?, updated_at = ? WHERE id = ?
      `, [newVersionId, now, id]);

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 回滚到版本成功 (提示词ID: ${id}, 版本ID: ${version_id})`);
      res.json({ message: '回滚成功', version: newVersion });
    } catch (err) {
      console.error('回滚失败:', err);
      res.status(500).json({ error: '回滚失败', message: err.message });
    }
  }

  // 获取分类统计
  static getCategoryCounts(req, res) {
    try {
      // 定义分类映射
      const categoryMappings = {
        'work': ['职业', '工作', '职场', 'career', 'job'],
        'business': ['商业', '商务', 'business', 'marketing', '销售'],
        'tools': ['工具', 'tool', '效率', 'productivity'],
        'language': ['语言', '翻译', 'language', 'translate', '英语'],
        'office': ['办公', 'office', '文档', 'excel', 'ppt'],
        'general': ['通用', 'general', '日常', '常用'],
        'writing': ['写作', '文案', 'writing', 'content', '创作'],
        'programming': ['编程', '代码', 'programming', 'code', '开发'],
        'emotion': ['情感', '心理', 'emotion', '情绪'],
        'education': ['教育', '学习', 'education', 'teaching', '培训'],
        'creative': ['创意', '创新', 'creative', '设计思维'],
        'academic': ['学术', '研究', 'academic', '论文'],
        'design': ['设计', 'UI', 'UX', 'design', '视觉'],
        'tech': ['技术', '科技', 'tech', 'AI', '人工智能'],
        'entertainment': ['娱乐', '游戏', 'entertainment', 'fun']
      };

      // 获取所有提示词的标签
      const result = database.getDB().exec(`
        SELECT tags FROM prompts WHERE tags IS NOT NULL AND tags != ""
      `);

      const rows = result.length > 0 ? result[0].values : [];
      const categoryCounts = {};

      // 初始化所有分类计数为0
      Object.keys(categoryMappings).forEach(category => {
        categoryCounts[category] = 0;
      });

      // 统计每个提示词属于哪些分类
      rows.forEach(row => {
        try {
          const tags = JSON.parse(row[0]);
          Object.entries(categoryMappings).forEach(([category, keywords]) => {
            const matches = tags.some(tag => {
              const tagLower = tag.toLowerCase();
              return keywords.some(keyword =>
                tagLower.includes(keyword.toLowerCase()) ||
                keyword.toLowerCase().includes(tagLower)
              );
            });

            if (matches) {
              categoryCounts[category]++;
            }
          });
        } catch (e) {
          // 忽略解析错误
        }
      });

      res.json(categoryCounts);
    } catch (err) {
      console.error('获取分类统计失败:', err);
      res.status(500).json({ error: '获取分类统计失败', message: err.message });
    }
  }

  // 根据分类获取提示词
  static getPromptsByCategory(req, res) {
    try {
      const { category } = req.params;

      const result = database.getDB().exec(`
        SELECT p.id, p.name, p.source, p.notes, p.tags, p.pinned,
               p.created_at, p.updated_at, p.current_version_id,
               v.content, v.version
        FROM prompts p
        LEFT JOIN versions v ON v.id = p.current_version_id
        WHERE p.tags LIKE ?
        ORDER BY p.pinned DESC, p.updated_at DESC
      `, [`%"${category}":%`]);

      const rows = result.length > 0 ? result[0].values : [];
      const prompts = rows.map(row => ({
        id: row[0],
        name: row[1],
        source: row[2],
        notes: row[3],
        tags: row[4] ? JSON.parse(row[4]) : [],
        pinned: row[5] === 1,
        created_at: row[6],
        updated_at: row[7],
        current_version_id: row[8],
        content: row[9] || '',
        version: row[10] || '1.0.0'
      }));

      res.json(prompts);
    } catch (err) {
      console.error('根据分类获取提示词失败:', err);
      res.status(500).json({ error: '获取提示词失败', message: err.message });
    }
  }
}

export default PromptController;
