/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 380px;
  min-height: 500px;
  max-height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  color: #333;
  background: #f8f9fa;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  width: 24px;
  height: 24px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-icon:hover {
  background: #e9ecef;
  color: #495057;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px 0 0 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #007bff;
}

.search-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-left: none;
  border-radius: 0 6px 6px 0;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
}

.search-btn:hover {
  background: #e9ecef;
}

/* 状态消息 */
.status-message {
  padding: 8px 16px;
  margin: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  text-align: center;
}

.status-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* 提示词容器 */
.prompts-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-message {
  padding: 20px;
  text-align: center;
  color: #6c757d;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.error-text h3 {
  color: #dc3545;
  margin-bottom: 8px;
}

.error-text p {
  margin-bottom: 8px;
  line-height: 1.4;
}

.error-text code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.btn-primary {
  margin-top: 12px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: background 0.2s;
}

.btn-primary:hover {
  background: #0056b3;
}

/* 提示词列表 */
.prompts-list {
  padding: 0 8px;
}

.prompt-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.prompt-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.prompt-item.copied {
  border-color: #28a745;
  background: #f8fff9;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.prompt-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  line-height: 1.3;
  flex: 1;
  margin-right: 8px;
}

.prompt-source {
  font-size: 11px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.prompt-content {
  color: #495057;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  font-size: 11px;
  color: #007bff;
  background: #e7f3ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #b3d9ff;
}

.copy-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #28a745;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.copy-indicator.show {
  opacity: 1;
}

/* 空状态 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: #495057;
}

/* 底部 */
.footer {
  padding: 12px 16px;
  background: #fff;
  border-top: 1px solid #e9ecef;
}

.tips {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
}

/* 工具类 */
.hidden {
  display: none !important;
}

/* 滚动条样式 */
.prompts-container::-webkit-scrollbar {
  width: 6px;
}

.prompts-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.prompts-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.prompts-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 400px) {
  body {
    width: 320px;
  }

  .prompt-content {
    -webkit-line-clamp: 2;
  }
}
