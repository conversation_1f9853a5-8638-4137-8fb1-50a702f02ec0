# 扩展图标说明

由于无法直接生成图标文件，请按照以下步骤创建扩展图标：

## 图标要求

需要创建以下尺寸的图标文件：
- `icon16.png` - 16x16 像素
- `icon32.png` - 32x32 像素  
- `icon48.png` - 48x48 像素
- `icon128.png` - 128x128 像素

## 设计建议

1. **主题色彩**：使用蓝色系 (#007bff) 作为主色调，与主项目保持一致
2. **图标元素**：可以使用以下元素组合：
   - 📝 文档/笔记图标
   - 🤖 AI/机器人元素
   - 💬 对话气泡
   - ⚡ 闪电（表示快速）

3. **设计风格**：
   - 简洁现代
   - 圆角设计
   - 清晰可辨识

## 临时解决方案

如果暂时没有图标，可以：

1. 使用在线图标生成器（如 favicon.io）
2. 从免费图标库下载（如 Feather Icons, Heroicons）
3. 使用简单的文字图标（如字母 "P"）

## 图标放置

将生成的图标文件放在 `chrome-extension/icons/` 目录下，确保文件名与 manifest.json 中的配置一致。
