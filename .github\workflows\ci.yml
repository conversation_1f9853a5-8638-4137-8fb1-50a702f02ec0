name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Select test type'
        required: true
        default: 'basic'
        type: choice
        options:
        - basic
        - full
        - tauri-only
      platforms:
        description: 'Select platforms to build'
        required: false
        type: choice
        default: 'all'
        options:
        - all
        - ubuntu
        - windows
        - macos
      upload_artifacts:
        description: 'Upload build artifacts'
        required: false
        type: boolean
        default: false

jobs:
  # 前端快速检查
  frontend-checks:
    if: github.event_name != 'workflow_dispatch' || (github.event.inputs.test_type == 'basic' || github.event.inputs.test_type == 'full')
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Type check
        run: pnpm typecheck
      
      - name: Build Frontend
        run: pnpm build

  # 后端快速检查
  backend-check:
    if: github.event_name != 'workflow_dispatch' || (github.event.inputs.test_type == 'basic' || github.event.inputs.test_type == 'full')
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable
      
      - name: Install Tauri system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libwebkit2gtk-4.1-dev \
            libgtk-3-dev \
            libayatana-appindicator3-dev \
            librsvg2-dev \
            build-essential \
            curl \
            wget \
            file \
            patchelf \
            libssl-dev \
            libsqlite3-dev \
            pkg-config \
            libglib2.0-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libatk1.0-dev \
            libgdk-pixbuf2.0-dev
      
      - name: Set PKG_CONFIG_PATH
        run: |
          echo "PKG_CONFIG_PATH=/usr/lib/x86_64-linux-gnu/pkgconfig:/usr/share/pkgconfig:/usr/lib/pkgconfig" >> $GITHUB_ENV
          
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri
      
      - name: Verify GTK installation
        run: |
          echo "=== Checking pkg-config for GTK ==="
          pkg-config --exists gtk+-3.0 && echo "GTK 3.0 found" || echo "GTK 3.0 NOT found"
          pkg-config --exists gdk-3.0 && echo "GDK 3.0 found" || echo "GDK 3.0 NOT found"
          echo "=== PKG_CONFIG_PATH ==="
          echo $PKG_CONFIG_PATH
          
      - name: Check Rust dependencies
        run: |
          cd src-tauri
          echo "=== Running cargo check ==="
          cargo check --lib

  # Ubuntu 构建测试
  test-tauri-ubuntu:
    needs: [frontend-checks, backend-check]
    if: >-
      always() &&
      github.event_name == 'workflow_dispatch' &&
      (github.event.inputs.test_type == 'tauri-only' || github.event.inputs.test_type == 'full') &&
      (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'ubuntu'))
    runs-on: ubuntu-22.04
    container: ubuntu:22.04
    steps:
      - name: Prepare base tools in container
        run: apt-get update && apt-get install -y git ca-certificates curl xz-utils
      
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Install Rust system dependencies
        run: |
          apt-get update && apt-get install -y \
            libwebkit2gtk-4.1-dev libgtk-3-dev libayatana-appindicator3-dev \
            librsvg2-dev build-essential curl wget file patchelf libssl-dev
      
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri
      
      - name: Build Tauri app (Debug)
        run: pnpm tauri build --debug
      
      - name: Upload build artifacts (if requested)
        if: ${{ github.event.inputs.upload_artifacts == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-debug-ubuntu
          path: src-tauri/target/debug/bundle/

  # Windows 构建测试
  test-tauri-windows:
    needs: [frontend-checks, backend-check]
    if: >-
      always() &&
      github.event_name == 'workflow_dispatch' &&
      (github.event.inputs.test_type == 'tauri-only' || github.event.inputs.test_type == 'full') &&
      (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'windows'))
    runs-on: windows-2022
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri
      
      - name: Build Tauri app (Debug)
        run: pnpm tauri build --debug
      
      - name: Upload build artifacts (if requested)
        if: ${{ github.event.inputs.upload_artifacts == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-debug-windows
          path: src-tauri/target/debug/bundle/

  # macOS 构建测试
  test-tauri-macos:
    needs: [frontend-checks, backend-check]
    if: >-
      always() &&
      github.event_name == 'workflow_dispatch' &&
      (github.event.inputs.test_type == 'tauri-only' || github.event.inputs.test_type == 'full') &&
      (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'macos'))
    runs-on: macos-13
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri
      
      - name: Build Tauri app (Debug)
        run: pnpm tauri build --debug
      
      - name: Upload build artifacts (if requested)
        if: ${{ github.event.inputs.upload_artifacts == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-debug-macos
          path: src-tauri/target/debug/bundle/