import express from 'express';
import SettingsController from '../controllers/settingsController.js';

const router = express.Router();

// 获取单个设置
router.get('/:key', SettingsController.getSetting);

// 设置单个配置
router.put('/:key', SettingsController.setSetting);

// 获取所有设置
router.get('/', SettingsController.getAllSettings);

// 批量更新设置
router.put('/', SettingsController.updateSettings);

// 重置设置为默认值
router.post('/reset', SettingsController.resetSettings);

export default router;
