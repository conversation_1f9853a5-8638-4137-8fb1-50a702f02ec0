import database from '../models/database.js';
import fs from 'fs';
import path from 'path';

class ExportController {
  // 导出数据
  static exportData(req, res) {
    try {
      // 获取所有提示词
      const promptResult = database.getDB().exec(`
        SELECT id, name, source, notes, tags, pinned, created_at, updated_at, current_version_id
        FROM prompts ORDER BY id
      `);

      const promptRows = promptResult.length > 0 ? promptResult[0].values : [];
      const prompts = [];

      // 为每个提示词获取版本信息
      promptRows.forEach(promptRow => {
        const promptWithVersions = {
          id: promptRow[0],
          name: promptRow[1],
          source: promptRow[2],
          notes: promptRow[3],
          tags: promptRow[4] ? JSON.parse(promptRow[4]) : [],
          pinned: promptRow[5] === 1,
          created_at: promptRow[6],
          updated_at: promptRow[7],
          current_version_id: promptRow[8],
          versions: []
        };

        // 获取该提示词的所有版本
        const versionResult = database.getDB().exec(`
          SELECT id, prompt_id, version, content, created_at, parent_version_id
          FROM versions WHERE prompt_id = ? ORDER BY created_at DESC
        `, [promptRow[0]]);

        const versionRows = versionResult.length > 0 ? versionResult[0].values : [];
        promptWithVersions.versions = versionRows.map(versionRow => ({
          id: versionRow[0],
          prompt_id: versionRow[1],
          version: versionRow[2],
          content: versionRow[3],
          created_at: versionRow[4],
          parent_version_id: versionRow[5]
        }));

        prompts.push(promptWithVersions);
      });

      // 获取设置
      const settingsResult = database.getDB().exec('SELECT key, value FROM settings');
      const settingsRows = settingsResult.length > 0 ? settingsResult[0].values : [];
      const settings = {};
      settingsRows.forEach(row => {
        settings[row[0]] = row[1];
      });

      const exportData = {
        prompts,
        settings,
        export_time: new Date().toISOString()
      };

      console.log(`✅ 导出数据成功: ${prompts.length} 个提示词`);
      res.json(exportData);
    } catch (err) {
      console.error('导出失败:', err);
      res.status(500).json({ error: '导出失败', message: err.message });
    }
  }

  // 导出数据到文件
  static exportDataToFile(req, res) {
    try {
      const { filePath } = req.body;

      if (!filePath) {
        return res.status(400).json({ error: '文件路径不能为空' });
      }

      // 获取导出数据
      const exportData = ExportController.getExportDataSync();

      const jsonStr = JSON.stringify(exportData, null, 2);
      
      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 写入文件
      fs.writeFileSync(filePath, jsonStr, 'utf8');
      
      console.log(`✅ 导出文件成功: ${filePath}`);
      res.json({ 
        message: '导出成功', 
        filePath,
        count: exportData.prompts.length 
      });
    } catch (err) {
      console.error('导出文件失败:', err);
      res.status(500).json({ error: '导出文件失败', message: err.message });
    }
  }

  // 导入数据
  static importData(req, res) {
    try {
      const { data } = req.body;

      if (!data || !data.prompts || !Array.isArray(data.prompts)) {
        return res.status(400).json({ error: '导入数据格式错误' });
      }

      // 清空现有数据
      database.getDB().run('DELETE FROM versions');
      database.getDB().run('DELETE FROM prompts');

      // 导入提示词和版本
      data.prompts.forEach(prompt => {
        // 插入提示词
        database.getDB().run(`
          INSERT INTO prompts (id, name, source, notes, tags, pinned, created_at, updated_at, current_version_id)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          prompt.id,
          prompt.name,
          prompt.source,
          prompt.notes,
          JSON.stringify(prompt.tags || []),
          prompt.pinned ? 1 : 0,
          prompt.created_at,
          prompt.updated_at,
          prompt.current_version_id
        ]);

        // 插入版本
        prompt.versions.forEach(version => {
          database.getDB().run(`
            INSERT INTO versions (id, prompt_id, version, content, created_at, parent_version_id)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [
            version.id,
            version.prompt_id,
            version.version,
            version.content,
            version.created_at,
            version.parent_version_id
          ]);
        });
      });

      // 导入设置
      if (data.settings) {
        Object.entries(data.settings).forEach(([key, value]) => {
          database.getDB().run('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)', [key, value]);
        });
      }

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 导入数据成功: ${data.prompts.length} 个提示词`);
      res.json({ message: '导入成功', count: data.prompts.length });
    } catch (err) {
      console.error('导入失败:', err);
      res.status(500).json({ error: '导入失败', message: err.message });
    }
  }

  // 获取导出数据的同步方法
  static getExportDataSync() {
    // 获取所有提示词
    const promptResult = database.getDB().exec(`
      SELECT id, name, source, notes, tags, pinned, created_at, updated_at, current_version_id
      FROM prompts ORDER BY id
    `);

    const promptRows = promptResult.length > 0 ? promptResult[0].values : [];
    const prompts = [];

    // 为每个提示词获取版本信息
    promptRows.forEach(promptRow => {
      const promptWithVersions = {
        id: promptRow[0],
        name: promptRow[1],
        source: promptRow[2],
        notes: promptRow[3],
        tags: promptRow[4] ? JSON.parse(promptRow[4]) : [],
        pinned: promptRow[5] === 1,
        created_at: promptRow[6],
        updated_at: promptRow[7],
        current_version_id: promptRow[8],
        versions: []
      };

      // 获取该提示词的所有版本
      const versionResult = database.getDB().exec(`
        SELECT id, prompt_id, version, content, created_at, parent_version_id
        FROM versions WHERE prompt_id = ? ORDER BY created_at DESC
      `, [promptRow[0]]);

      const versionRows = versionResult.length > 0 ? versionResult[0].values : [];
      promptWithVersions.versions = versionRows.map(versionRow => ({
        id: versionRow[0],
        prompt_id: versionRow[1],
        version: versionRow[2],
        content: versionRow[3],
        created_at: versionRow[4],
        parent_version_id: versionRow[5]
      }));

      prompts.push(promptWithVersions);
    });

    // 获取设置
    const settingsResult = database.getDB().exec('SELECT key, value FROM settings');
    const settingsRows = settingsResult.length > 0 ? settingsResult[0].values : [];
    const settings = {};
    settingsRows.forEach(row => {
      settings[row[0]] = row[1];
    });

    return {
      prompts,
      settings,
      export_time: new Date().toISOString()
    };
  }
}

export default ExportController;
