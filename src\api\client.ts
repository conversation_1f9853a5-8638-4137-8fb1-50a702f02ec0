// API客户端 - 替换Tauri的invoke函数

const API_BASE_URL = '/api';

class ApiClient {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const config = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: '请求失败' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API请求失败 [${endpoint}]:`, error);
      throw error;
    }
  }

  // 获取所有提示词
  async getAllPrompts(): Promise<any[]> {
    return this.request<any[]>('/prompts');
  }

  // 搜索提示词
  async searchPrompts(query: string, tags: string[] = [], sources: string[] = []): Promise<any> {
    return this.request('/prompts/search', {
      method: 'POST',
      body: JSON.stringify({ query, tags, sources }),
    });
  }

  // 创建提示词
  async createPrompt(data: {
    name: string;
    source?: string | null;
    notes?: string | null;
    tags: string[];
    content: string;
  }): Promise<{ id: number; message: string }> {
    return this.request('/prompts', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 更新提示词
  async updatePrompt(
    id: number,
    data: {
      name: string;
      source?: string | null;
      notes?: string | null;
      tags: string[];
      content: string;
      save_as_version?: boolean;
      version_type?: string;
    }
  ): Promise<{ message: string; version?: string }> {
    return this.request(`/prompts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // 删除提示词
  async deletePrompt(id: number): Promise<{ message: string }> {
    return this.request(`/prompts/${id}`, {
      method: 'DELETE',
    });
  }

  // 切换置顶状态
  async togglePin(id: number): Promise<{ message: string }> {
    return this.request(`/prompts/${id}/pin`, {
      method: 'PATCH',
    });
  }

  // 获取提示词版本历史
  async getPromptVersions(promptId: number): Promise<any[]> {
    return this.request<any[]>(`/prompts/${promptId}/versions`);
  }

  // 回滚到指定版本
  async rollbackToVersion(
    promptId: number,
    versionId: number,
    versionType: string = 'patch'
  ): Promise<{ message: string; version: string }> {
    return this.request(`/prompts/${promptId}/rollback`, {
      method: 'POST',
      body: JSON.stringify({ version_id: versionId, version_type: versionType }),
    });
  }

  // 获取所有标签
  async getAllTags(): Promise<string[]> {
    return this.request<string[]>('/prompts/meta/tags');
  }

  // 获取所有来源
  async getAllSources(): Promise<string[]> {
    return this.request<string[]>('/prompts/meta/sources');
  }

  // 获取分类统计
  async getCategoryCounts(): Promise<Record<string, number>> {
    return this.request<Record<string, number>>('/prompts/meta/categories');
  }

  // 根据分类获取提示词
  async getPromptsByCategory(category: string): Promise<any[]> {
    return this.request<any[]>(`/prompts/category/${category}`);
  }

  // 导出数据
  async exportData(): Promise<any> {
    return this.request('/export/data');
  }

  // 导出数据到文件
  async exportDataToFile(filePath: string): Promise<{ message: string; filePath: string; count: number }> {
    return this.request('/export/file', {
      method: 'POST',
      body: JSON.stringify({ filePath }),
    });
  }

  // 导入数据
  async importData(data: any): Promise<{ message: string; count?: number }> {
    return this.request('/export/import', {
      method: 'POST',
      body: JSON.stringify({ data }),
    });
  }

  // 获取设置
  async getSetting(key: string): Promise<{ value: string | null }> {
    return this.request<{ value: string | null }>(`/settings/${key}`);
  }

  // 设置配置
  async setSetting(key: string, value: string): Promise<{ message: string }> {
    return this.request(`/settings/${key}`, {
      method: 'PUT',
      body: JSON.stringify({ value }),
    });
  }

  // 获取所有设置
  async getAllSettings(): Promise<Record<string, string>> {
    return this.request<Record<string, string>>('/settings');
  }

  // 批量更新设置
  async updateSettings(settings: Record<string, string>): Promise<{ message: string; count: number }> {
    return this.request('/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // 重置设置
  async resetSettings(): Promise<{ message: string; settings: Record<string, string> }> {
    return this.request('/settings/reset', {
      method: 'POST',
    });
  }

  // 健康检查
  async healthCheck(): Promise<{ status: string; message: string; timestamp: string }> {
    return this.request('/health');
  }
}

// 创建单例实例
const apiClient = new ApiClient();

// 导出invoke函数的替代品
export const invoke = async (command: string, args: any = {}): Promise<any> => {
  console.log(`🔄 API调用: ${command}`, args);
  
  try {
    let result: any;

    switch (command) {
      case 'get_all_prompts':
        result = await apiClient.getAllPrompts();
        break;

      case 'search_prompts':
        result = await apiClient.searchPrompts(args.query, args.tags, args.sources);
        break;

      case 'create_prompt':
        result = await apiClient.createPrompt(args);
        break;

      case 'update_prompt':
        result = await apiClient.updatePrompt(args.id, args);
        break;

      case 'delete_prompt':
        result = await apiClient.deletePrompt(args.id);
        break;

      case 'toggle_pin':
        result = await apiClient.togglePin(args.id);
        break;

      case 'get_prompt_versions':
        result = await apiClient.getPromptVersions(args.promptId);
        break;

      case 'rollback_to_version':
        result = await apiClient.rollbackToVersion(args.promptId, args.versionId, args.versionType);
        break;

      case 'get_all_tags':
        result = await apiClient.getAllTags();
        break;

      case 'get_all_sources':
        result = await apiClient.getAllSources();
        break;

      case 'get_category_counts':
        result = await apiClient.getCategoryCounts();
        break;

      case 'get_prompts_by_category':
        result = await apiClient.getPromptsByCategory(args.category);
        break;

      case 'export_data':
        result = await apiClient.exportData();
        break;

      case 'export_data_to_file':
        result = await apiClient.exportDataToFile(args.filePath);
        break;

      case 'import_data':
        result = await apiClient.importData(args.data);
        break;

      case 'get_setting':
        const settingResult = await apiClient.getSetting(args.key);
        result = settingResult.value;
        break;

      case 'set_setting':
        result = await apiClient.setSetting(args.key, args.value);
        break;

      default:
        throw new Error(`未知的API命令: ${command}`);
    }

    console.log(`✅ API调用成功: ${command}`, result);
    return result;
  } catch (error) {
    console.error(`❌ API调用失败: ${command}`, error);
    throw error;
  }
};

export default apiClient;
