# Prompt Tools 服务器启动指南

## 概述

Prompt Tools 已成功从 Tauri 桌面应用转换为基于 Node.js 的 Web 服务器。现在您可以通过浏览器访问所有功能。

## 系统要求

- **Node.js**: 版本 16.0 或更高
- **操作系统**: Windows、macOS 或 Linux
- **浏览器**: Chrome、Firefox、Safari 或 Edge（现代版本）

## 快速启动

### Windows 用户

1. 双击 `start.bat` 文件
2. 等待服务器启动
3. 浏览器会自动打开 http://localhost:18080

### macOS/Linux 用户

1. 打开终端
2. 进入项目目录
3. 运行: `chmod +x start.sh && ./start.sh`
4. 在浏览器中访问 http://localhost:18080

### 手动启动

如果启动脚本无法使用，可以手动执行以下步骤：

```bash
# 1. 安装依赖（首次运行）
npm install

# 2. 构建前端
npm run client:build

# 3. 启动服务器
node server/src/app.js
```

## 访问地址

- **主页**: http://localhost:18080
- **API 端点**: http://localhost:18080/api
- **健康检查**: http://localhost:18080/api/health

## Chrome 浏览器扩展 🔌

项目还提供了 Chrome 浏览器扩展，让您可以在任何网页上快速管理和使用提示词！

### 扩展功能
- **📋 快速查看**：点击扩展图标查看所有提示词
- **⚡ 一键复制**：点击提示词即可复制到剪贴板
- **➕ 右键添加**：选中网页文本后右键添加到 Prompt Tools
- **🔍 智能搜索**：支持按名称、内容、标签搜索
- **⌨️ 快捷键**：`Ctrl+Shift+P` 快速添加选中文本

### 安装扩展
1. 确保 Prompt Tools 服务器正在运行
2. 参考 `chrome-extension/install.md` 详细安装指南
3. 在 Chrome 中加载 `chrome-extension` 文件夹

### 快速开始
```bash
# 1. 启动服务器
npm start

# 2. 生成扩展图标（可选）
# 打开 chrome-extension/create-icons.html 下载图标

# 3. 在 Chrome 中安装扩展
# 访问 chrome://extensions/ 加载扩展文件夹
```

## 功能说明

### 已迁移的功能

✅ **提示词管理**
- 创建、编辑、删除提示词
- 标签和分类管理
- 搜索和筛选

✅ **版本控制**
- 提示词版本历史
- 版本回滚功能

✅ **数据导入导出**
- JSON 格式数据导出
- 数据备份和恢复

✅ **设置管理**
- 应用配置保存
- 用户偏好设置

### 新增功能

🆕 **Web 访问**
- 通过浏览器访问，无需安装桌面应用
- 支持多设备访问

🆕 **API 接口**
- RESTful API 设计
- 支持第三方集成

## 数据迁移

原有的数据库文件 `prompts.db` 会自动被新服务器识别和使用，无需手动迁移数据。

## 故障排除

### 端口被占用

如果端口 18080 被占用，可以通过环境变量指定其他端口：

```bash
# Windows
set PORT=3000 && node server/src/app.js

# macOS/Linux
PORT=3000 node server/src/app.js
```

### 依赖安装失败

如果 npm install 失败，尝试：

```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
```

### 数据库问题

如果遇到数据库相关错误：

1. 检查 `prompts.db` 文件是否存在
2. 确保有读写权限
3. 如需重置，删除 `prompts.db` 文件，服务器会自动创建新的数据库

## 停止服务器

在终端中按 `Ctrl+C` 即可停止服务器。

## 技术架构

- **后端**: Node.js + Express.js
- **数据库**: SQLite (sql.js)
- **前端**: 原有的 TypeScript/HTML/CSS
- **API**: RESTful HTTP 接口

## 支持

如有问题，请检查：
1. Node.js 版本是否符合要求
2. 网络端口是否被占用
3. 文件权限是否正确

---

**注意**: 这是从 Tauri 桌面应用转换而来的 Web 服务器版本。所有原有功能都已保留，并增加了 Web 访问能力。
