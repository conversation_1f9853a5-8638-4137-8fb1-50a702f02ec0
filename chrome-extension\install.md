# Prompt Tools Chrome 扩展安装指南

## 📋 安装前准备

### 1. 确保服务器运行
在安装扩展之前，请确保 Prompt Tools 服务器正在运行：

```bash
# 进入项目根目录
cd /path/to/Prompt-Tools-main

# 启动服务器
npm start
# 或者
node server/src/app.js
```

确认服务器运行在：`http://localhost:18080`

### 2. 准备扩展图标
由于技术限制，需要手动添加扩展图标：

1. 在 `chrome-extension/icons/` 目录下创建以下图标文件：
   - `icon16.png` (16x16 像素)
   - `icon32.png` (32x32 像素)
   - `icon48.png` (48x48 像素)
   - `icon128.png` (128x128 像素)

2. **快速获取图标的方法**：
   - 访问 [favicon.io](https://favicon.io/favicon-generator/)
   - 输入字母 "P" 或 "PT"
   - 选择蓝色主题 (#007bff)
   - 下载并重命名为对应尺寸

## 🚀 安装步骤

### 步骤 1：打开 Chrome 扩展管理页面
1. 打开 Chrome 浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 按回车键进入扩展管理页面

### 步骤 2：启用开发者模式
1. 在扩展管理页面右上角找到"开发者模式"开关
2. 点击开关，启用开发者模式
3. 页面会显示额外的开发者选项

### 步骤 3：加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 在文件选择器中导航到项目目录
3. 选择 `chrome-extension` 文件夹
4. 点击"选择文件夹"

### 步骤 4：确认安装
1. 扩展应该出现在扩展列表中
2. 确保扩展状态为"已启用"
3. 浏览器工具栏应该显示 Prompt Tools 图标

## ✅ 验证安装

### 测试基本功能
1. **测试弹窗**：
   - 点击浏览器工具栏中的 Prompt Tools 图标
   - 应该看到提示词列表弹窗

2. **测试右键菜单**：
   - 在任意网页上选中一段文本
   - 右键点击，应该看到"添加到 Prompt Tools"选项

3. **测试快捷键**：
   - 选中网页文本
   - 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
   - 应该看到成功添加的提示

## 🔧 故障排除

### 扩展无法加载
**问题**：点击"加载已解压的扩展程序"后出现错误

**解决方案**：
1. 检查 `manifest.json` 文件是否存在且格式正确
2. 确保所有必需的文件都在 `chrome-extension` 目录中
3. 查看错误详情，根据提示修复问题

### 无法连接服务器
**问题**：扩展显示"连接失败"

**解决方案**：
1. 确认 Prompt Tools 服务器正在运行
2. 访问 `http://localhost:18080/api/health` 检查服务器状态
3. 检查防火墙是否阻止了本地连接

### 右键菜单不显示
**问题**：选中文本后右键没有看到菜单选项

**解决方案**：
1. 确保扩展已启用
2. 刷新网页后重试
3. 检查是否在支持的网页上（http/https）

### 图标不显示
**问题**：扩展图标显示为默认图标

**解决方案**：
1. 确保在 `chrome-extension/icons/` 目录下有所需的图标文件
2. 检查图标文件名是否与 `manifest.json` 中的配置一致
3. 重新加载扩展

## 📱 使用技巧

### 快速操作
- **快速复制**：点击扩展图标 → 点击任意提示词
- **快速添加**：选中文本 → `Ctrl+Shift+P`
- **批量管理**：点击扩展弹窗中的"打开网页版"

### 搜索技巧
- 支持搜索提示词名称、内容、标签
- 使用关键词快速定位特定类型的提示词
- 搜索框支持实时过滤

### 最佳实践
1. **定期备份**：使用网页版的导出功能备份数据
2. **合理分类**：为提示词添加有意义的标签
3. **及时清理**：删除不再使用的提示词

## 🔄 更新扩展

当扩展代码更新时：
1. 在扩展管理页面找到 Prompt Tools 扩展
2. 点击刷新按钮（🔄）
3. 扩展会重新加载最新代码

## 🗑️ 卸载扩展

如需卸载扩展：
1. 在扩展管理页面找到 Prompt Tools 扩展
2. 点击"移除"按钮
3. 确认删除

**注意**：卸载扩展不会影响 Prompt Tools 服务器中的数据。

## 📞 获取帮助

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查 Prompt Tools 服务器日志
3. 参考项目文档或提交 Issue

---

安装完成后，您就可以在任何网页上快速管理和使用 AI 提示词了！🎉
