import initSqlJs from 'sql.js';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 数据库文件路径
const DB_PATH = path.join(__dirname, '../../../prompts.db');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.SQL = null;
    this.isInitialized = false;
    this.initPromise = this.init();
  }

  async init() {
    try {
      // 初始化sql.js
      this.SQL = await initSqlJs();

      // 确保数据库目录存在
      const dbDir = path.dirname(DB_PATH);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // 尝试加载现有数据库文件
      let dbData = null;
      if (fs.existsSync(DB_PATH)) {
        dbData = fs.readFileSync(DB_PATH);
      }

      // 创建数据库实例
      this.db = new this.SQL.Database(dbData);

      console.log('✅ 数据库连接成功:', DB_PATH);

      // 启用外键约束
      this.db.run('PRAGMA foreign_keys = ON');

      this.initTables();
      this.isInitialized = true;
    } catch (err) {
      console.error('数据库连接失败:', err);
      throw err;
    }
  }

  async waitForInit() {
    if (!this.isInitialized) {
      await this.initPromise;
    }
    return this.db;
  }

  initTables() {
    try {
      // 创建提示词表
      this.db.run(`
        CREATE TABLE IF NOT EXISTS prompts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          source TEXT,
          notes TEXT,
          tags TEXT,
          pinned INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          current_version_id INTEGER
        )
      `);

      // 创建版本表
      this.db.run(`
        CREATE TABLE IF NOT EXISTS versions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          prompt_id INTEGER NOT NULL,
          version TEXT NOT NULL,
          content TEXT NOT NULL,
          created_at TEXT NOT NULL,
          parent_version_id INTEGER,
          FOREIGN KEY(prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
        )
      `);

      // 创建设置表
      this.db.run(`
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL
        )
      `);

      // 插入默认设置
      this.db.run(`
        INSERT OR IGNORE INTO settings (key, value)
        VALUES ('version_cleanup_threshold', '200')
      `);

      // 创建索引
      this.db.run(`
        CREATE INDEX IF NOT EXISTS idx_prompts_updated_at ON prompts(updated_at)
      `);

      this.db.run(`
        CREATE INDEX IF NOT EXISTS idx_versions_prompt_id ON versions(prompt_id)
      `);

      // 保存数据库到文件
      this.saveToFile();

      // 初始化默认提示词
      this.initDefaultPrompts();
    } catch (err) {
      console.error('初始化数据库表失败:', err);
    }
  }

  initDefaultPrompts() {
    try {
      // 检查是否已经初始化过
      const result = this.db.exec('SELECT COUNT(*) as count FROM prompts');

      // 如果已有提示词，则不重复初始化
      if (result.length > 0 && result[0].values.length > 0 && result[0].values[0][0] > 0) {
        return;
      }

      console.log('🔄 正在初始化默认提示词...');
      this.insertDefaultPrompts();
    } catch (err) {
      console.error('检查提示词数量失败:', err);
    }
  }

  insertDefaultPrompts() {
    const defaultPrompts = [
      {
        name: "专业邮件撰写助手",
        content: "你是一位专业的商务邮件撰写专家。请帮我撰写一封专业、礼貌且有效的邮件。\n\n要求：\n1. 语言正式但不失亲和力\n2. 结构清晰，逻辑性强\n3. 突出重点信息\n4. 包含适当的开头和结尾\n\n请告诉我：\n- 邮件主题\n- 收件人\n- 邮件目的\n- 关键信息",
        tags: ["工作", "邮件", "商务", "沟通"],
        source: "内部整理",
        notes: "适用于各种商务邮件场景",
        pinned: true
      },
      {
        name: "代码审查专家",
        content: "你是一位经验丰富的高级开发工程师，擅长代码审查和质量改进。请对我提供的代码进行全面审查。\n\n审查要点：\n1. 代码逻辑和算法效率\n2. 代码风格和规范性\n3. 安全性问题\n4. 可维护性和可读性\n5. 性能优化建议\n6. 潜在的bug和边界情况\n\n请提供：\n- 具体的问题指出\n- 改进建议和最佳实践\n- 重构建议（如需要）\n- 相关的代码示例\n\n请贴出需要审查的代码。",
        tags: ["编程", "代码审查", "质量", "优化"],
        source: "内部整理",
        notes: "提高代码质量和开发效率",
        pinned: true
      },
      {
        name: "文案创作大师",
        content: "你是一位资深的文案创作专家，擅长各种类型的文案写作。请根据我的需求创作吸引人的文案。\n\n创作要求：\n1. 明确目标受众\n2. 突出核心卖点\n3. 使用有说服力的语言\n4. 包含行动召唤\n5. 符合品牌调性\n\n请告诉我：\n- 文案类型（广告、推广、介绍等）\n- 目标受众\n- 产品/服务特点\n- 期望达到的效果",
        tags: ["文案", "创作", "营销", "写作"],
        source: "内部整理",
        notes: "适用于各种商业文案需求",
        pinned: true
      },
      {
        name: "学习计划制定师",
        content: "你是一位专业的学习规划师，擅长为不同需求的学习者制定个性化学习计划。\n\n制定计划时请考虑：\n1. 学习者的基础水平\n2. 学习目标和时间限制\n3. 学习风格和偏好\n4. 可用的学习资源\n5. 进度评估方法\n\n请提供：\n- 阶段性学习目标\n- 具体的学习内容和顺序\n- 时间分配建议\n- 学习方法推荐\n- 进度检查节点\n\n请告诉我你的学习目标和现有基础。",
        tags: ["学习", "教育", "计划", "个性化"],
        source: "内部整理",
        notes: "适用于各种学科和技能学习",
        pinned: false
      }
    ];

    const now = new Date().toISOString();

    try {
      for (const prompt of defaultPrompts) {
        const tagsJson = JSON.stringify(prompt.tags);

        // 创建提示词
        this.db.run(`
          INSERT INTO prompts (name, source, notes, tags, pinned, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [prompt.name, prompt.source, prompt.notes, tagsJson, prompt.pinned ? 1 : 0, now, now]);

        // 获取插入的提示词ID
        const promptResult = this.db.exec('SELECT last_insert_rowid() as id');
        const promptId = promptResult[0].values[0][0];

        // 创建初始版本
        this.db.run(`
          INSERT INTO versions (prompt_id, version, content, created_at, parent_version_id)
          VALUES (?, '1.0.0', ?, ?, NULL)
        `, [promptId, prompt.content, now]);

        // 获取插入的版本ID
        const versionResult = this.db.exec('SELECT last_insert_rowid() as id');
        const versionId = versionResult[0].values[0][0];

        // 更新当前版本ID
        this.db.run('UPDATE prompts SET current_version_id = ? WHERE id = ?', [versionId, promptId]);
      }

      // 保存数据库到文件
      this.saveToFile();

      console.log(`✅ 已初始化 ${defaultPrompts.length} 个默认提示词`);
    } catch (err) {
      console.error('插入默认提示词失败:', err);
    }
  }

  // 保存数据库到文件
  saveToFile() {
    if (this.db) {
      try {
        const data = this.db.export();
        fs.writeFileSync(DB_PATH, data);
      } catch (err) {
        console.error('保存数据库文件失败:', err);
      }
    }
  }

  // 获取数据库实例
  getDB() {
    if (!this.isInitialized) {
      throw new Error('数据库尚未初始化完成');
    }
    return this.db;
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      try {
        this.saveToFile();
        this.db.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭数据库失败:', err);
      }
    }
  }
}

// 创建单例实例
const database = new DatabaseManager();

export default database;
