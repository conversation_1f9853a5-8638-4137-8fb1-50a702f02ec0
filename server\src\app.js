import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// 导入路由
import promptRoutes from './routes/prompts.js';
import settingsRoutes from './routes/settings.js';
import exportRoutes from './routes/export.js';

// 导入数据库
import database from './models/database.js';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 18080;

// 中间件配置
app.use(cors({
  origin: true, // 允许所有来源（包括 Chrome 扩展）
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务 - 提供前端文件
app.use(express.static(path.join(__dirname, '../../dist')));

// API路由
app.use('/api/prompts', promptRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/export', exportRoutes);

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({ 
    status: '正常运行', 
    message: 'Prompt Tools 服务器运行正常',
    timestamp: new Date().toISOString()
  });
});

// 前端路由处理 - 所有非API请求都返回index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../../dist/index.html'));
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ 
    error: '服务器内部错误',
    message: err.message 
  });
});

// 启动服务器函数
async function startServer() {
  try {
    // 等待数据库初始化完成
    console.log('🔄 正在初始化数据库...');
    await database.waitForInit();
    console.log('✅ 数据库初始化完成');

    // 启动服务器
    const server = app.listen(PORT, () => {
      console.log(`🚀 Prompt Tools 服务器已启动`);
      console.log(`📍 本地访问地址: http://localhost:${PORT}`);
      console.log(`🔧 API 端点: http://localhost:${PORT}/api`);
      console.log(`📊 健康检查: http://localhost:${PORT}/api/health`);
    });

    server.on('error', (err) => {
      console.error('❌ 服务器错误:', err);
      if (err.code === 'EADDRINUSE') {
        console.error(`端口 ${PORT} 已被占用，请尝试其他端口`);
      }
      process.exit(1);
    });

  } catch (err) {
    console.error('❌ 服务器启动失败:', err);
    process.exit(1);
  }
}

// 启动服务器
startServer();

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🔄 正在关闭服务器...');
  database.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🔄 正在关闭服务器...');
  database.close();
  process.exit(0);
});

export default app;
