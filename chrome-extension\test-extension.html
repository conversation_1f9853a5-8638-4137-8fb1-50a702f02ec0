<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Tools 扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-text {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            cursor: pointer;
            user-select: text;
        }
        .test-text:hover {
            background: #fff8e1;
        }
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        h1, h2 {
            color: #2c3e50;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 Prompt Tools Chrome 扩展测试页面</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>这个页面用于测试 Prompt Tools Chrome 扩展的各项功能。请按照以下步骤进行测试：</p>
        <ol>
            <li>确保 Prompt Tools 服务器正在运行（http://localhost:18080）</li>
            <li>确保 Chrome 扩展已正确安装并启用</li>
            <li>按照下面的测试项目逐一验证功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 测试 1：扩展图标和弹窗</h2>
        <div class="instructions">
            <strong>操作步骤：</strong>
            <ol>
                <li>查看浏览器工具栏，确认 Prompt Tools 扩展图标显示正常</li>
                <li>点击扩展图标，应该弹出提示词列表窗口</li>
                <li>检查弹窗界面是否正常显示</li>
            </ol>
        </div>
        <div id="test1Status" class="status info">等待测试...</div>
        <button class="test-button" onclick="testPopup()">测试弹窗功能</button>
    </div>

    <div class="test-section">
        <h2>➕ 测试 2：右键菜单添加功能</h2>
        <div class="instructions">
            <strong>操作步骤：</strong>
            <ol>
                <li>选中下面的测试文本</li>
                <li>右键点击选中的文本</li>
                <li>选择"添加到 Prompt Tools"</li>
                <li>应该看到成功添加的通知</li>
            </ol>
        </div>
        
        <div class="test-text" id="testText1">
            这是一个用于测试右键菜单功能的示例文本。请选中这段文字，然后右键选择"添加到 Prompt Tools"来测试扩展的添加功能。这段文字包含了足够的内容来验证扩展是否能正确处理文本选择和添加操作。
        </div>
        
        <div id="test2Status" class="status info">请按照上述步骤操作...</div>
    </div>

    <div class="test-section">
        <h2>⌨️ 测试 3：快捷键功能</h2>
        <div class="instructions">
            <strong>操作步骤：</strong>
            <ol>
                <li>选中下面的测试文本</li>
                <li>按 <code>Ctrl+Shift+P</code> (Windows/Linux) 或 <code>Cmd+Shift+P</code> (Mac)</li>
                <li>应该看到成功添加的提示消息</li>
            </ol>
        </div>
        
        <div class="test-text" id="testText2">
            快捷键测试文本：使用 Ctrl+Shift+P 或 Cmd+Shift+P 可以快速将选中的文本添加到 Prompt Tools。这个功能让用户能够更高效地收集和管理提示词，无需使用鼠标右键菜单。
        </div>
        
        <div id="test3Status" class="status info">请按照上述步骤操作...</div>
    </div>

    <div class="test-section">
        <h2>📋 测试 4：复制功能</h2>
        <div class="instructions">
            <strong>操作步骤：</strong>
            <ol>
                <li>点击扩展图标打开弹窗</li>
                <li>点击任意一个提示词</li>
                <li>提示词应该被复制到剪贴板</li>
                <li>在下面的文本框中粘贴验证</li>
            </ol>
        </div>
        
        <textarea id="pasteArea" placeholder="在这里粘贴复制的提示词内容..." style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
        
        <div id="test4Status" class="status info">请按照上述步骤操作...</div>
        <button class="test-button" onclick="checkPaste()">检查粘贴内容</button>
    </div>

    <div class="test-section">
        <h2>🔍 测试 5：搜索功能</h2>
        <div class="instructions">
            <strong>操作步骤：</strong>
            <ol>
                <li>点击扩展图标打开弹窗</li>
                <li>在搜索框中输入关键词</li>
                <li>验证搜索结果是否正确过滤</li>
                <li>清空搜索框，确认显示所有提示词</li>
            </ol>
        </div>
        
        <div id="test5Status" class="status info">请按照上述步骤操作...</div>
    </div>

    <div class="test-section">
        <h2>🌐 测试 6：服务器连接</h2>
        <div class="instructions">
            <strong>自动测试服务器连接状态：</strong>
        </div>
        
        <div id="serverStatus" class="status info">正在检查服务器连接...</div>
        <button class="test-button" onclick="testServerConnection()">重新测试连接</button>
    </div>

    <div class="test-section">
        <h2>📊 测试结果总结</h2>
        <div id="testSummary" class="status info">
            请完成上述所有测试项目，然后查看总结结果。
        </div>
        <button class="test-button" onclick="generateSummary()">生成测试总结</button>
    </div>

    <script>
        // 测试服务器连接
        async function testServerConnection() {
            const statusEl = document.getElementById('serverStatus');
            statusEl.textContent = '正在检查服务器连接...';
            statusEl.className = 'status info';
            
            try {
                const response = await fetch('http://localhost:18080/api/health');
                if (response.ok) {
                    const data = await response.json();
                    statusEl.textContent = '✅ 服务器连接正常: ' + data.message;
                    statusEl.className = 'status success';
                } else {
                    throw new Error('服务器响应异常');
                }
            } catch (error) {
                statusEl.textContent = '❌ 服务器连接失败: 请确保服务器运行在 http://localhost:18080';
                statusEl.className = 'status error';
            }
        }

        // 测试弹窗功能
        function testPopup() {
            const statusEl = document.getElementById('test1Status');
            statusEl.textContent = '请点击浏览器工具栏中的 Prompt Tools 图标，检查弹窗是否正常显示';
            statusEl.className = 'status info';
        }

        // 检查粘贴内容
        function checkPaste() {
            const pasteArea = document.getElementById('pasteArea');
            const statusEl = document.getElementById('test4Status');
            
            if (pasteArea.value.trim()) {
                statusEl.textContent = '✅ 检测到粘贴内容，复制功能正常';
                statusEl.className = 'status success';
            } else {
                statusEl.textContent = '❌ 未检测到粘贴内容，请先从扩展弹窗复制提示词';
                statusEl.className = 'status error';
            }
        }

        // 生成测试总结
        function generateSummary() {
            const summaryEl = document.getElementById('testSummary');
            const serverStatus = document.getElementById('serverStatus').className;
            const pasteContent = document.getElementById('pasteArea').value.trim();
            
            let passedTests = 0;
            let totalTests = 6;
            
            if (serverStatus.includes('success')) passedTests++;
            if (pasteContent) passedTests++;
            
            const percentage = Math.round((passedTests / totalTests) * 100);
            
            summaryEl.innerHTML = `
                <strong>测试完成情况：</strong><br>
                自动检测通过: ${passedTests}/2 项<br>
                手动测试项目: 4 项（需要手动验证）<br>
                <br>
                <strong>建议：</strong><br>
                ${serverStatus.includes('success') ? '✅' : '❌'} 服务器连接<br>
                ${pasteContent ? '✅' : '❌'} 复制功能<br>
                ⚠️ 其他功能需要手动测试验证
            `;
            
            if (passedTests === 2) {
                summaryEl.className = 'status success';
            } else {
                summaryEl.className = 'status error';
            }
        }

        // 页面加载时自动测试服务器连接
        window.addEventListener('load', () => {
            testServerConnection();
        });
    </script>
</body>
</html>
