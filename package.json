{"name": "prompt-tools", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "npm run build && node server/src/app.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "build": "npm run client:build", "client:build": "tsc && vite build", "client:dev": "vite", "server:dev": "nodemon server/src/app.js", "server:start": "node server/src/app.js", "preview": "vite preview", "typecheck": "tsc --noEmit", "test": "echo \"No tests specified yet\" && exit 0"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "sql.js": "^1.13.0"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.2", "typescript": "~5.6.2", "vite": "^6.0.3"}}