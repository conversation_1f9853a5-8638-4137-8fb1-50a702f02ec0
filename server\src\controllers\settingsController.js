import database from '../models/database.js';

class SettingsController {
  // 获取设置
  static getSetting(req, res) {
    try {
      const { key } = req.params;

      const result = database.getDB().exec('SELECT value FROM settings WHERE key = ?', [key]);

      if (result.length > 0 && result[0].values.length > 0) {
        res.json({ value: result[0].values[0][0] });
      } else {
        res.json({ value: null });
      }
    } catch (err) {
      console.error('获取设置失败:', err);
      res.status(500).json({ error: '获取设置失败', message: err.message });
    }
  }

  // 设置配置
  static setSetting(req, res) {
    try {
      const { key } = req.params;
      const { value } = req.body;

      if (!value) {
        return res.status(400).json({ error: '设置值不能为空' });
      }

      database.getDB().run('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)', [key, value]);

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 设置配置成功: ${key} = ${value}`);
      res.json({ message: '设置成功' });
    } catch (err) {
      console.error('设置配置失败:', err);
      res.status(500).json({ error: '设置失败', message: err.message });
    }
  }

  // 获取所有设置
  static getAllSettings(req, res) {
    try {
      const result = database.getDB().exec('SELECT key, value FROM settings');
      const rows = result.length > 0 ? result[0].values : [];

      const settings = {};
      rows.forEach(row => {
        settings[row[0]] = row[1];
      });

      res.json(settings);
    } catch (err) {
      console.error('获取所有设置失败:', err);
      res.status(500).json({ error: '获取设置失败', message: err.message });
    }
  }

  // 批量更新设置
  static updateSettings(req, res) {
    try {
      const settings = req.body;

      if (!settings || typeof settings !== 'object') {
        return res.status(400).json({ error: '设置数据格式错误' });
      }

      const entries = Object.entries(settings);
      if (entries.length === 0) {
        return res.status(400).json({ error: '没有要更新的设置' });
      }

      // 批量更新设置
      for (const [key, value] of entries) {
        database.getDB().run('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)', [key, value]);
      }

      // 保存数据库
      database.saveToFile();

      console.log(`✅ 批量更新设置成功: ${entries.length} 项`);
      res.json({ message: '设置更新成功', count: entries.length });
    } catch (err) {
      console.error('批量更新设置失败:', err);
      res.status(500).json({ error: '更新设置失败', message: err.message });
    }
  }

  // 重置设置为默认值
  static resetSettings(req, res) {
    try {
      const defaultSettings = {
        'version_cleanup_threshold': '200'
      };

      // 清空现有设置
      database.getDB().run('DELETE FROM settings');

      // 插入默认设置
      Object.entries(defaultSettings).forEach(([key, value]) => {
        database.getDB().run('INSERT INTO settings (key, value) VALUES (?, ?)', [key, value]);
      });

      // 保存数据库
      database.saveToFile();

      console.log('✅ 设置已重置为默认值');
      res.json({ message: '设置已重置为默认值', settings: defaultSettings });
    } catch (err) {
      console.error('重置设置失败:', err);
      res.status(500).json({ error: '重置设置失败', message: err.message });
    }
  }
}

export default SettingsController;
