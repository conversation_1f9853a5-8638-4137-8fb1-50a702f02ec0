import express from 'express';
import PromptController from '../controllers/promptController.js';

const router = express.Router();

// 获取所有提示词
router.get('/', PromptController.getAllPrompts);

// 搜索提示词
router.post('/search', PromptController.searchPrompts);

// 创建提示词
router.post('/', PromptController.createPrompt);

// 更新提示词
router.put('/:id', PromptController.updatePrompt);

// 删除提示词
router.delete('/:id', PromptController.deletePrompt);

// 切换置顶状态
router.patch('/:id/pin', PromptController.togglePin);

// 获取提示词版本历史
router.get('/:id/versions', PromptController.getPromptVersions);

// 回滚到指定版本
router.post('/:id/rollback', PromptController.rollbackToVersion);

// 获取所有标签
router.get('/meta/tags', PromptController.getTagsEndpoint);

// 获取所有来源
router.get('/meta/sources', PromptController.getSourcesEndpoint);

// 获取分类统计
router.get('/meta/categories', PromptController.getCategoryCounts);

// 根据分类获取提示词
router.get('/category/:category', PromptController.getPromptsByCategory);

export default router;
