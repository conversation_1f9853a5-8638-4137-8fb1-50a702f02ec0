# Prompt Tools Chrome 扩展

这是 Prompt Tools 项目的 Chrome 浏览器扩展，让您可以在任何网页上快速管理和使用 AI 提示词。

## 🚀 功能特性

### 核心功能
- **📋 快速查看**：点击扩展图标查看所有已保存的提示词
- **⚡ 一键复制**：点击任意提示词即可复制到剪贴板
- **🔍 智能搜索**：支持按名称、内容、标签搜索提示词
- **➕ 右键添加**：选中网页文本后右键选择"添加到 Prompt Tools"
- **⌨️ 快捷键**：使用 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac) 快速添加选中文本

### 界面特点
- 🎨 现代化设计，与主项目风格一致
- 🌏 完整中文界面
- 📱 响应式布局，适配不同屏幕
- ✨ 流畅的动画效果和用户反馈

## 📦 安装步骤

### 前置条件
确保 Prompt Tools 服务器正在运行：
```bash
# 在项目根目录执行
npm start
# 或
node server/src/app.js
```
服务器应该运行在 `http://localhost:18080`

### 安装扩展

1. **准备扩展文件**
   ```bash
   # 确保扩展文件夹存在
   cd chrome-extension/
   ls -la  # 应该看到 manifest.json 等文件
   ```

2. **添加扩展图标**（重要）
   - 在 `chrome-extension/icons/` 目录下添加以下图标文件：
     - `icon16.png` (16x16)
     - `icon32.png` (32x32) 
     - `icon48.png` (48x48)
     - `icon128.png` (128x128)
   - 可以使用在线工具生成或从图标库下载

3. **在 Chrome 中加载扩展**
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-extension` 文件夹
   - 扩展安装完成！

## 🎯 使用教程

### 查看和复制提示词
1. 点击浏览器工具栏中的 Prompt Tools 图标
2. 在弹窗中浏览所有提示词
3. 使用搜索框快速查找特定提示词
4. 点击任意提示词即可复制到剪贴板

### 添加新提示词

**方法一：右键菜单**
1. 在任何网页上选中想要保存的文本
2. 右键点击选中的文本
3. 选择"添加到 Prompt Tools"
4. 系统会自动保存并显示成功提示

**方法二：快捷键**
1. 选中网页上的文本
2. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
3. 文本会自动添加到 Prompt Tools

### 管理提示词
- 点击扩展弹窗右上角的"打开网页版"按钮
- 在完整的 Web 界面中进行详细管理

## 🔧 技术实现

### 架构设计
```
Chrome Extension
├── manifest.json     # 扩展配置文件
├── popup.html/css/js  # 弹窗界面
├── background.js      # 后台脚本（右键菜单、通知）
├── content.js         # 内容脚本（快捷键、页面交互）
└── icons/            # 扩展图标
```

### API 通信
扩展通过 HTTP 请求与本地 Prompt Tools 服务器通信：
- **获取提示词**：`GET http://localhost:18080/api/prompts`
- **添加提示词**：`POST http://localhost:18080/api/prompts`

### 权限说明
扩展请求的权限及用途：
- `activeTab`：获取当前标签页信息
- `contextMenus`：创建右键菜单
- `storage`：存储扩展设置
- `clipboardWrite`：复制文本到剪贴板
- `host_permissions`：访问本地服务器 API

## 🛠️ 开发和调试

### 开发环境设置
1. 确保 Prompt Tools 服务器运行在 `http://localhost:18080`
2. 在 Chrome 扩展管理页面启用"开发者模式"
3. 修改代码后点击扩展的"刷新"按钮

### 调试方法
- **弹窗调试**：右键点击扩展图标 → "检查弹出式窗口"
- **后台脚本调试**：扩展管理页面 → 点击"service worker"
- **内容脚本调试**：在网页上按 F12 → Console 标签

### 常见问题排查

**扩展无法加载**
- 检查 `manifest.json` 语法是否正确
- 确保所有引用的文件都存在
- 查看 Chrome 扩展管理页面的错误信息

**无法连接服务器**
- 确认 Prompt Tools 服务器正在运行
- 检查服务器地址是否为 `http://localhost:18080`
- 查看浏览器控制台的网络错误

**右键菜单不显示**
- 确保在网页上选中了文本
- 检查扩展权限是否正确授予
- 重新加载扩展

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 📋 支持查看和复制提示词
- ➕ 支持右键添加新提示词
- 🔍 支持搜索功能
- ⌨️ 支持快捷键操作

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

### 代码规范
- 使用 ES6+ 语法
- 遵循现有代码风格
- 添加必要的注释
- 确保功能完整测试

## 📄 许可证

与主项目保持一致的许可证。
