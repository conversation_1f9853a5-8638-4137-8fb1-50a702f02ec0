name: Manual Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string
      platforms:
        description: 'Select platforms to build'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - ubuntu
        - windows
        - macos
        - ubuntu-windows
        - ubuntu-macos
        - windows-macos
      prerelease:
        description: 'Mark as pre-release'
        required: false
        type: boolean
        default: false

permissions:
  contents: write

jobs:
  build-ubuntu:
    if: ${{ contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'ubuntu') }}
    runs-on: ubuntu-22.04
    container: ubuntu:22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install system deps for Linux
        run: |
          apt-get update && apt-get install -y \
            libwebkit2gtk-4.1-dev libgtk-3-dev libayatana-appindicator3-dev \
            librsvg2-dev build-essential curl wget file patchelf libssl-dev \
            xdg-utils desktop-file-utils

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable

      - name: Cache cargo registry
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            src-tauri/target
          key: ubuntu-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ubuntu-cargo-

      - name: Build Tauri app
        run: pnpm tauri build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-linux-x64
          path: |
            src-tauri/target/release/bundle/appimage/*.AppImage
            src-tauri/target/release/bundle/deb/*.deb
            src-tauri/target/release/bundle/rpm/*.rpm
          retention-days: 7

  build-windows:
    if: ${{ contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'windows') }}
    runs-on: windows-2022
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable

      - name: Cache cargo registry
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            src-tauri/target
          key: windows-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            windows-cargo-

      - name: Build Tauri app
        run: pnpm tauri build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-windows-x64
          path: |
            src-tauri/target/release/bundle/nsis/*.exe
            src-tauri/target/release/bundle/msi/*.msi
          retention-days: 7

  build-macos-intel:
    if: ${{ contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'macos') }}
    runs-on: macos-13
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: x86_64-apple-darwin

      - name: Cache cargo registry
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            src-tauri/target
          key: macos-intel-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            macos-intel-cargo-

      - name: Build Tauri app
        run: pnpm tauri build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-macos-x64
          path: |
            src-tauri/target/release/bundle/dmg/*.dmg
            src-tauri/target/release/bundle/macos/*.app
          retention-days: 7

  build-macos-arm:
    if: ${{ contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'macos') }}
    runs-on: macos-14
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: aarch64-apple-darwin

      - name: Cache cargo registry
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            src-tauri/target
          key: macos-arm-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            macos-arm-cargo-

      - name: Build Tauri app
        run: npm run tauri:build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: prompt-tools-macos-arm64
          path: |
            src-tauri/target/release/bundle/dmg/*.dmg
            src-tauri/target/release/bundle/macos/*.app
          retention-days: 7

  release:
    needs: [build-ubuntu, build-windows, build-macos-intel, build-macos-arm]
    if: always()
    runs-on: ubuntu-22.04
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
      
      - name: Display structure of downloaded files
        run: ls -R artifacts
      
      - name: Create GitHub Release and upload assets
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.event.inputs.version }}
          name: Release ${{ github.event.inputs.version }}
          files: |
            artifacts/**/*.dmg
            artifacts/**/*.app
            artifacts/**/*.AppImage
            artifacts/**/*.deb
            artifacts/**/*.rpm
            artifacts/**/*setup*.exe
            artifacts/**/*.msi
          generate_release_notes: true
          draft: false
          prerelease: ${{ github.event.inputs.prerelease }}