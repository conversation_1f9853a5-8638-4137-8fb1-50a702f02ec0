/* CSS 变量定义 */
:root {
  --primary: #007bff;
  --primary-hover: #0056b3;
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #17a2b8;
  --light: #f8f9fa;
  --dark: #343a40;
  --muted: #6c757d;
  --star: #ffc107;

  --bg: #ffffff;
  --fg: #212529;
  --card: #ffffff;
  --border: #dee2e6;
  --shadow: rgba(0, 0, 0, 0.1);
  --text-on-primary: #ffffff;
  --sidebar-bg: #f8f9fa;
  --sidebar-border: #e9ecef;
  --nav-hover: #e9ecef;
  --nav-active: #007bff;
  --nav-active-bg: rgba(0, 123, 255, 0.1);

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-4xl: 6rem;

  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 1rem;

  --sidebar-width: 240px;
}

/* 深色主题 */
.theme-dark {
  --bg: #1a1a1a;
  --fg: #ffffff;
  --card: #2d2d2d;
  --border: #404040;
  --shadow: rgba(0, 0, 0, 0.3);
  --muted: #a0a0a0;
  --light: #404040;
  --dark: #f8f9fa;
  --sidebar-bg: #2d2d2d;
  --sidebar-border: #404040;
  --nav-hover: #404040;
  --nav-active-bg: rgba(0, 123, 255, 0.2);
}

/* 重置 */
* { 
  margin: 0; 
  padding: 0; 
  box-sizing: border-box; 
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg);
  color: var(--fg);
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#app { 
  min-height: 100vh; 
  display: flex; 
}

/* 左侧导航栏 */
.sidebar {
  width: var(--sidebar-width);
  background: var(--sidebar-bg);
  border-right: 1px solid var(--sidebar-border);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 100;
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--sidebar-border);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary);
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-md) 0;
}

.nav-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  margin: 0 var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--font-size-sm);
  color: var(--fg);
}

.nav-item:hover {
  background: var(--nav-hover);
}

.nav-item.active {
  background: var(--nav-active-bg);
  color: var(--nav-active);
  font-weight: 500;
}

.nav-item i {
  width: 16px;
  text-align: center;
  opacity: 0.7;
}

.nav-item.active i {
  opacity: 1;
}

.nav-item .count {
  margin-left: auto;
  font-size: var(--font-size-xs);
  color: var(--muted);
  background: var(--border);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.nav-item.active .count {
  background: var(--nav-active);
  color: white;
}

/* 主内容区域 */
.main-container {
  flex: 1;
  margin-left: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 顶部工具栏 */
.main-header {
  background: var(--card);
  border-bottom: 1px solid var(--border);
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-left {
  flex: 1;
  max-width: 400px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 搜索框 */
.search-container { 
  position: relative; 
  width: 100%; 
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted);
  pointer-events: none;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) calc(var(--spacing-lg) + 24px);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background: var(--bg);
  color: var(--fg);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
  height: 42px;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--primary) 10%, transparent);
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: var(--spacing-xl);
  background: var(--bg);
}

/* 提示词网格 */
.prompt-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.prompt-grid.list-view {
  grid-template-columns: 1fr;
}

/* 提示词卡片 */
.prompt-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.prompt-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow);
  border-color: var(--primary);
}

.prompt-card.pinned {
  border-color: var(--warning);
  background: color-mix(in srgb, var(--warning) 3%, var(--card));
}

.pin-indicator {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  color: var(--warning);
  font-size: var(--font-size-sm);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-md);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--fg);
  margin: 0;
  flex: 1;
  line-height: 1.3;
}

.card-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.prompt-card:hover .card-actions {
  opacity: 1;
}

.card-meta {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  font-size: var(--font-size-xs);
  color: var(--muted);
  margin-bottom: var(--spacing-md);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.card-tags {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.tag {
  background: var(--light);
  border: 1px solid var(--border);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 12px;
  font-size: var(--font-size-xs);
  color: var(--muted);
  transition: all 0.2s ease;
}

.card-content {
  font-size: var(--font-size-sm);
  color: var(--fg);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: color-mix(in srgb, var(--bg) 50%, var(--card));
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  overflow: hidden;
  position: relative;
}

.card-notes {
  font-size: var(--font-size-sm);
  color: var(--muted);
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.card-notes i {
  opacity: 0.7;
  margin-top: 2px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background: var(--bg);
  color: var(--fg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 42px;
}

.btn:hover {
  background: var(--light);
  transform: translateY(-1px);
}

.btn.btn-primary {
  background: var(--primary);
  color: var(--text-on-primary);
  border-color: var(--primary);
}

.btn.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
  min-width: 36px;
  padding: var(--spacing-sm);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  color: var(--primary);
  background: color-mix(in srgb, var(--primary) 8%, transparent);
  transform: scale(1.05);
}

.btn-icon.btn-danger {
  color: var(--danger);
}

.btn-icon.btn-danger:hover {
  background: color-mix(in srgb, var(--danger) 8%, transparent);
}

/* 工具栏组 */
.toolbar-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 下拉菜单 */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 12px var(--shadow);
  min-width: 160px;
  z-index: 1000;
  display: none;
  padding: var(--spacing-sm) 0;
}

.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: transparent;
  color: var(--fg);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background: var(--light);
}

.dropdown-divider {
  height: 1px;
  background: var(--border);
  margin: var(--spacing-sm) 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-4xl);
  border: 2px dashed var(--border);
  border-radius: var(--radius-xl);
  background: color-mix(in srgb, var(--card) 50%, transparent);
}

.empty-state i {
  font-size: 4rem;
  color: var(--muted);
  opacity: 0.5;
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--fg);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state p {
  font-size: var(--font-size-base);
  color: var(--muted);
  margin: 0;
}

/* 模态框 */
.modal {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-container {
  position: relative;
  background: var(--card);
  border-radius: var(--radius-xl);
  width: 85%;
  max-width: 520px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 12px 40px var(--shadow);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--fg);
}

.close-btn {
  background: transparent;
  border: none;
  color: var(--muted);
  font-size: var(--font-size-2xl);
  line-height: 1;
  cursor: pointer;
  padding: var(--spacing-xs);
}

.close-btn:hover {
  color: var(--fg);
}

.modal-form {
  padding: var(--spacing-lg);
  overflow: auto;
}

.form-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.field-label {
  font-weight: 500;
  color: var(--fg);
}

.required {
  color: var(--danger);
  margin-right: 4px;
}

.field-input, .field-textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background: var(--bg);
  color: var(--fg);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.field-input:focus, .field-textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--primary) 10%, transparent);
}

.field-textarea {
  resize: vertical;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  line-height: 1.5;
}

.textarea-container {
  position: relative;
}

.textarea-actions {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  border: 1px solid var(--border);
  background: var(--card);
  color: var(--muted);
  border-radius: var(--radius-md);
  padding: 6px 8px;
  cursor: pointer;
}

.action-btn:hover {
  color: var(--primary);
  background: color-mix(in srgb, var(--primary) 8%, transparent);
}

.token-counter {
  position: absolute;
  right: var(--spacing-sm);
  bottom: var(--spacing-sm);
  color: var(--muted);
  font-size: var(--font-size-xs);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--border);
}

.btn-cancel, .btn-submit {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  cursor: pointer;
  border: 1px solid var(--border);
}

.btn-cancel {
  background: var(--bg);
  color: var(--fg);
}

.btn-cancel:hover {
  background: var(--light);
}

.btn-submit {
  background: var(--success);
  color: #fff;
  border-color: var(--success);
}

.btn-submit:hover {
  background: #1e7e34;
  border-color: #1e7e34;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  :root {
    --sidebar-width: 200px;
  }
  
  .prompt-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-container {
    margin-left: 0;
  }
  
  .main-header {
    padding: var(--spacing-md);
  }
  
  .header-left {
    max-width: none;
  }
  
  .content-area {
    padding: var(--spacing-md);
  }
  
  .prompt-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .modal-container {
    width: 95%;
    margin: var(--spacing-md);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted);
}

/* 确认对话框样式 */
.confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.confirm-container {
  max-width: 280px;
  width: 85%;
  background: var(--card);
  border-radius: 10px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  padding: 0;
  overflow: visible;
  border: 1px solid var(--border);
  animation: confirmSlideIn 0.2s ease-out;
}

@keyframes confirmSlideIn {
  from {
    opacity: 0;
    transform: scale(0.96) translateY(-8px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.confirm-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 16px 12px;
  background: linear-gradient(135deg, #fff9f9 0%, #ffebeb 100%);
}

.theme-dark .confirm-icon {
  background: linear-gradient(135deg, #2a1e1e 0%, #332222 100%);
}

.confirm-icon i {
  font-size: 24px;
  color: #e53e3e;
  animation: gentlePulse 2s infinite;
}

@keyframes gentlePulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.02); opacity: 0.92; }
}

.confirm-content {
  padding: 0 16px 12px;
  text-align: center;
}

.confirm-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--fg);
  margin: 0 0 6px;
  line-height: 1.3;
}

.confirm-message {
  font-size: 12px;
  color: var(--muted);
  margin: 0;
  line-height: 1.4;
}

.confirm-actions {
  display: flex;
  gap: 6px;
  padding: 12px 16px 16px;
  background: transparent;
}

.confirm-actions .btn {
  flex: 1;
  justify-content: center;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 5px;
  transition: all 0.12s ease;
  border: 1px solid var(--border);
  min-height: 28px;
}

.confirm-actions .btn-secondary {
  background: var(--bg);
  color: var(--muted);
}

.confirm-actions .btn-secondary:hover {
  background: var(--light);
  color: var(--fg);
  transform: translateY(-0.5px);
}

.confirm-actions .btn-danger {
  background: #e53e3e;
  color: white;
  border-color: #e53e3e;
}

.confirm-actions .btn-danger:hover {
  background: #c53030;
  transform: translateY(-0.5px);
  box-shadow: 0 2px 8px rgba(229, 62, 62, 0.2);
}

.confirm-actions .btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.15);
}

.confirm-actions .btn i {
  margin-right: 3px;
  font-size: 10px;
}

/* 去使用这个提示词功能区域样式 */
.card-usage {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--light);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.usage-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--fg);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.usage-title::before {
  content: "🚀";
  font-size: 1.1em;
}

.usage-platforms {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-sm);
}

.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--fg);
  font-size: var(--font-size-xs);
  font-weight: 500;
  min-height: 60px;
  justify-content: center;
}

.platform-item:hover {
  background: var(--primary);
  color: var(--text-on-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
  border-color: var(--primary);
}

.platform-icon {
  width: 20px;
  height: 20px;
  margin-bottom: var(--spacing-xs);
  border-radius: var(--radius-sm);
  object-fit: contain;
  background: white;
  padding: 2px;
}

.platform-item span {
  font-size: var(--font-size-xs);
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .usage-platforms {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xs);
  }
  
  .platform-item {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
    min-height: 50px;
  }
  
  .platform-icon {
    width: 16px;
    height: 16px;
    margin-bottom: 4px;
  }
  
  .platform-item span {
    font-size: 10px;
  }
}

/* 列表视图下的调整 */
.list-view .usage-platforms {
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-xs);
}

.list-view .platform-item {
  padding: var(--spacing-xs);
  min-height: 50px;
}

.list-view .platform-icon {
  width: 16px;
  height: 16px;
}

.list-view .platform-item span {
  font-size: 10px;
}

/* 详情页面模态框样式 */
.modal-overlay {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.detail-modal {
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  background: var(--card);
  border-radius: var(--radius-xl);
  box-shadow: 0 12px 40px var(--shadow);
  overflow: hidden;
  position: relative;
}

.detail-body {
  padding: var(--spacing-xl);
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

.detail-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.detail-info {
  flex: 1;
  min-width: 300px;
}

.info-item {
  display: flex;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-md);
}

.info-item label {
  font-weight: 600;
  color: var(--muted);
  min-width: 80px;
  font-size: var(--font-size-sm);
}

.info-item span {
  color: var(--fg);
  font-size: var(--font-size-sm);
  flex: 1;
}

.detail-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.detail-actions .btn {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn.btn-outline {
  background: transparent;
  border: 1px solid var(--border);
  color: var(--fg);
}

.btn.btn-outline:hover {
  background: var(--light);
  border-color: var(--primary);
  color: var(--primary);
}

.detail-tags {
  margin-bottom: var(--spacing-xl);
}

.detail-tags label {
  display: block;
  font-weight: 600;
  color: var(--muted);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.tags-list {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.tags-list .tag {
  background: var(--primary);
  color: var(--text-on-primary);
  border: none;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 16px;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.detail-content {
  margin-bottom: var(--spacing-xl);
}

.detail-content label {
  display: block;
  font-weight: 600;
  color: var(--muted);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.content-display {
  background: var(--bg);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: var(--font-size-sm);
  line-height: 1.6;
  color: var(--fg);
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.detail-notes {
  margin-bottom: var(--spacing-lg);
}

.detail-notes label {
  display: block;
  font-weight: 600;
  color: var(--muted);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.notes-display {
  background: color-mix(in srgb, var(--warning) 5%, var(--bg));
  border: 1px solid color-mix(in srgb, var(--warning) 20%, var(--border));
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--fg);
  font-style: italic;
}

/* 隐藏空的详情项 */
.detail-tags:empty,
.detail-notes:empty,
#detailSourceItem:empty {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .detail-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .detail-body {
    padding: var(--spacing-lg);
  }
  
  .detail-meta {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .detail-info {
    min-width: auto;
  }
  
  .detail-actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .detail-actions .btn {
    flex: 1;
    min-width: 0;
  }
  
  .info-item {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .info-item label {
    min-width: auto;
  }
}

/* 优化按钮样式增强 */
.textarea-actions .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  background: var(--card);
  color: var(--primary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.textarea-actions .action-btn:hover {
  background: var(--primary);
  color: var(--text-on-primary);
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.textarea-actions .action-btn:active {
  transform: scale(0.95);
}

.textarea-actions .action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: var(--light);
  color: var(--muted);
}

.textarea-actions .action-btn i.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 确保textarea容器有足够的padding来容纳按钮 */
.textarea-container .field-textarea {
  padding-right: 50px;
}
